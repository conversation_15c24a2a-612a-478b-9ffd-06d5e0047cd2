const Koa = require('koa')
const cors = require('@koa/cors') // 跨域处理
const static = require('koa-static') // 静态资源服务
const path = require('path')
const { koaBody } = require('koa-body') //参数解析
const useRouter = require('./src/router') //路由注册
const VerifyMiddleware = require('./src/middleware/verify.middleware')

const app = new Koa()
app.useRouter = useRouter
app.use(cors())
app.use(koaBody())
app.use(
  static('./updates', {
    setHeaders: (ctx, filePath) => {
      const filename = path.basename(filePath)
      ctx.setHeader('Cache-Control', 'public, max-age=2678400000')
      ctx.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    }
  })
) // 静态资源服务
app.use(VerifyMiddleware.verifyAuth) // 身份验证
app.useRouter(app) // 统一注册路由
app.use(async (ctx, next) => {
  console.log(ctx.status)

  await next()
  console.log('到这里了')
})
app.listen(3001)
